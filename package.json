{"name": "tech-news-feed", "type": "module", "version": "0.1.0", "private": true, "sideEffects": false, "engines": {"node": "22.x"}, "scripts": {"dev": "next dev --turbopack", "build": "next build --webpack", "start": "next start", "lint": "next lint", "payload": "cross-env NODE_OPTIONS='--import tsx/esm' payload"}, "dependencies": {"@clerk/nextjs": "^6.36.0", "@neondatabase/serverless": "^1.0.2", "@next/third-parties": "^16.0.7", "@payloadcms/db-vercel-postgres": "^3.67.0", "@payloadcms/next": "^3.67.0", "@payloadcms/richtext-lexical": "^3.67.0", "@payloadcms/storage-vercel-blob": "^3.67.0", "@prisma/adapter-neon": "^6.19.0", "@prisma/client": "^6.19.0", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-slot": "^1.2.4", "@react-email/render": "^2.0.0", "@tailwindcss/typography": "^0.5.19", "@vercel/analytics": "^1.6.1", "@vercel/blob": "^2.0.0", "@vercel/speed-insights": "^1.3.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.3", "graphql": "^16.12.0", "lucide-react": "^0.556.0", "next": "^16.0.7", "payload": "^3.67.0", "prop-types": "^15.8.1", "react": "^19.2.1", "react-dom": "^19.2.1", "resend": "^6.5.2", "tailwind-merge": "^3.4.0", "tailwindcss-animate": "^1.0.7", "ws": "^8.18.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.3", "@tailwindcss/postcss": "^4.1.17", "@types/node": "^24.10.1", "@types/react": "^19.2.7", "@types/react-dom": "^19.2.3", "@types/ws": "^8.18.1", "autoprefixer": "^10.4.22", "cross-env": "^7.0.3", "eslint": "^9.39.1", "eslint-config-next": "16.0.7", "postcss": "^8", "prisma": "^6.19.0", "tailwindcss": "^4.1.17", "tsx": "^4.21.0", "typescript": "^5.9.3"}, "optionalDependencies": {"bufferutil": "^4.0.9"}}