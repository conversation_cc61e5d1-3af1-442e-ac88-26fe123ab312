# Resend Email Setup for Contact Form

## Overview

The contact form now uses Resend to send emails to `<EMAIL>`. Here's how to set it up:

## 1. Get Resend API Key

1. Go to [Resend.com](https://resend.com)
2. Sign up or log in to your account
3. Navigate to **API Keys** in the dashboard
4. Click **Create API Key**
5. Give it a name like "Tech News Contact Form"
6. Copy the API key (starts with `re_`)

## 2. Configure Environment Variables

Add your Resend API key to `.env.local`:

```bash
RESEND_API_KEY=re_your_actual_api_key_here
```

## 3. Domain Setup (Important!)

For production, you'll need to:

1. **Add your domain** to Resend:
   - Go to **Domains** in Resend dashboard
   - Add `tech-news.io`
   - Follow DNS verification steps

2. **Update the "from" address** in the API route:
   - Currently set to: `<EMAIL>`
   - This will work once domain is verified

## 4. Testing

### Development Testing:
- The form will work immediately with any valid Resend API key
- Emails will be sent from Resend's default domain
- All emails will go to `<EMAIL>`

### Production Setup:
- Verify your domain in Resend
- Update DNS records as instructed
- Test the contact form thoroughly

## 5. Email Template

The contact form sends HTML emails with:
- **Subject**: "Contact Form: [user's subject]"
- **From**: "Tech News Contact Form <<EMAIL>>"
- **To**: "<EMAIL>"
- **Content**: Formatted HTML with user details and message

## 6. Error Handling

The form includes:
- ✅ Field validation (all fields required)
- ✅ Email format validation
- ✅ Error display for users
- ✅ Server-side error handling
- ✅ Loading states

## 7. Security Features

- ✅ Server-side validation
- ✅ Rate limiting (via Resend)
- ✅ Input sanitization
- ✅ CORS protection
- ✅ Environment variable protection

## 8. Monitoring

Monitor your contact form via:
- **Resend Dashboard**: View sent emails, delivery status
- **Server Logs**: Check for any API errors
- **Form Analytics**: Track submission success rates

## Cost

Resend offers:
- **Free tier**: 3,000 emails/month
- **Paid plans**: Start at $20/month for 50,000 emails
- Perfect for contact forms with moderate traffic

## Support

If you encounter issues:
1. Check Resend dashboard for delivery status
2. Verify API key is correct
3. Ensure domain is properly configured
4. Check server logs for errors

The contact form is now production-ready! 🚀
