import { PrismaNeon } from '@prisma/adapter-neon'
import { PrismaClient } from '@prisma/client'

// Next.js loads .env automatically; no need for dotenv here
const connectionString = process.env.DATABASE_URL
if (!connectionString) {
  throw new Error('DATABASE_URL is not defined')
}

// Minimal Neon adapter configuration to avoid deprecation warnings
const adapter = new PrismaNeon({ connectionString })

// Avoid creating multiple PrismaClient instances in dev (HMR)
declare global {
  var prisma: PrismaClient | undefined
}

export const prisma = globalThis.prisma ?? new PrismaClient({
  adapter,
  log: ['error', 'warn'],
})

if (process.env.NODE_ENV !== 'production') globalThis.prisma = prisma