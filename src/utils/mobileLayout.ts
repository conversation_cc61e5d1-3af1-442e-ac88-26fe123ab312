/**
 * Client-side version that checks cookies and URL parameters
 * Used by client components that need to detect mobile layout
 */
export function isMobileLayoutClient(): boolean {
  if (typeof window === 'undefined') return false;

  // Check cookie first (set by middleware)
  const cookieValue = document.cookie
    .split('; ')
    .find(row => row.startsWith('mobile_layout='))
    ?.split('=')[1];

  if (cookieValue === 'true') return true;

  // Fallback to URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('mobile_layout') === 'true' ||
         urlParams.get('mobile_layout') === '1' ||
         urlParams.get('app') === 'mobile';
}
