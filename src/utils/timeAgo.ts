/**
 * Convert a date to a human-readable "time ago" format
 * @param date - Date string or Date object
 * @returns String like "2 minutes ago", "3 hours ago", "1 day ago"
 */
export function timeAgo(date: string | Date): string {
  const now = new Date();
  const past = new Date(date);
  const diffInSeconds = Math.floor((now.getTime() - past.getTime()) / 1000);

  // Handle invalid dates
  if (isNaN(diffInSeconds)) {
    return 'Unknown';
  }

  // Handle future dates (shouldn't happen but just in case)
  if (diffInSeconds < 0) {
    return 'Just now';
  }

  // Less than 1 minute
  if (diffInSeconds < 30) {
    return 'Just now';
  }

  if (diffInSeconds < 60) {
    return `${diffInSeconds} seconds ago`;
  }

  // Minutes
  const minutes = Math.floor(diffInSeconds / 60);
  if (minutes < 60) {
    return `${minutes} minute${minutes === 1 ? '' : 's'} ago`;
  }

  // Hours
  const hours = Math.floor(minutes / 60);
  if (hours < 24) {
    return `${hours} hour${hours === 1 ? '' : 's'} ago`;
  }

  // Days
  const days = Math.floor(hours / 24);
  if (days < 7) {
    return `${days} day${days === 1 ? '' : 's'} ago`;
  }

  // Weeks
  const weeks = Math.floor(days / 7);
  if (weeks < 5) {
    return `${weeks} week${weeks === 1 ? '' : 's'} ago`;
  }

  // Months (more accurate calculation)
  const monthsDiff = (now.getFullYear() - past.getFullYear()) * 12 + (now.getMonth() - past.getMonth());
  if (monthsDiff < 12) {
    return `${monthsDiff} month${monthsDiff === 1 ? '' : 's'} ago`;
  }

  // Years
  const yearsDiff = now.getFullYear() - past.getFullYear();
  return `${yearsDiff} year${yearsDiff === 1 ? '' : 's'} ago`;
}
