import { clerkMiddleware } from "@clerk/nextjs/server";
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export default clerkMiddleware((auth, request: NextRequest) => {
  const response = NextResponse.next();

  // Check for mobile layout indicators from Flutter app
  const mobileLayoutHeader = request.headers.get('mobile_layout');
  const mobileAppHeader = request.headers.get('x-mobile-app');
  const requestedWithHeader = request.headers.get('x-requested-with');
  const userAgent = request.headers.get('user-agent') || '';
  const urlParams = request.nextUrl.searchParams;

  const isMobileLayout =
    mobileLayoutHeader === 'true' ||
    mobileAppHeader === 'flutter-tech-news' ||
    requestedWithHeader === 'TechNewsApp' ||
    userAgent.includes('TechNewsApp/1.0') ||
    userAgent.includes('TechNewsFlutter') ||
    userAgent.includes('TechNewsMobile') ||
    urlParams.get('mobile_layout') === 'true' ||
    urlParams.get('mobile_layout') === '1' ||
    urlParams.get('app') === 'mobile';

  // Set a cookie to indicate mobile layout for client-side components
  if (isMobileLayout) {
    response.cookies.set('mobile_layout', 'true', {
      httpOnly: false, // Allow client-side access
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 // 24 hours
    });
  } else {
    response.cookies.delete('mobile_layout');
  }

  return response;
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    "/((?!_next|[^?]*\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)",
    // Always run for API routes
    "/(api|trpc)(.*)",
  ],
};
