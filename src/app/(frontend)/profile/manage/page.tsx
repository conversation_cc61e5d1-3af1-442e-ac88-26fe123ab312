import { UserProfile } from '@clerk/nextjs';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';

export default function ManageProfilePage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <Link
            href="/profile"
            className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to profile
          </Link>
        </div>

        <div className="flex justify-center">
          <UserProfile 
            appearance={{
              elements: {
                card: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700',
                headerTitle: 'text-gray-900 dark:text-white',
                headerSubtitle: 'text-gray-600 dark:text-gray-400',
                formButtonPrimary: 'bg-blue-600 hover:bg-blue-700 text-sm normal-case',
                formFieldLabel: 'text-gray-700 dark:text-gray-300',
                formFieldInput: 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white',
                footerActionLink: 'text-blue-600 hover:text-blue-500',
              },
            }}
          />
        </div>
      </div>
    </div>
  );
}
