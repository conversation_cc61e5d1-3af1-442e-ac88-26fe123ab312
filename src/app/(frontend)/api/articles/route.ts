import { NextResponse } from 'next/server';
import { newsData, NewsArticle } from '@/data/news';
import {getLatestArticles} from "@/app/(frontend)/utils/article";

/*function getFilteredArticles(filter: string): NewsArticle[] {
  switch (filter) {
    case 'Popular':
      return [...newsData].sort((a, b) => b.likes - a.likes);
    case 'Following':
      // In a real app, this would filter based on user's followed sources/categories
      return newsData.filter(article => 
        ['TechCrunch', 'The Verge'].includes(article.source)
      );
    case 'Latest':
    default:
      // Assuming the data is already sorted by timestamp
      return newsData;
  }
}*/

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '10');
  const filter = searchParams.get('filter') || 'Recent';
  const tagsParam = searchParams.get('tags');
  const sourcesParam = searchParams.get('sources');

  // Parse tags from comma-separated string
  const selectedTags = tagsParam ? tagsParam.split(',').filter(tag => tag.trim()) : [];

  // Parse sources from comma-separated string
  const selectedSources = sourcesParam ? sourcesParam.split(',').map(id => parseInt(id)).filter(id => !isNaN(id)) : [];

  const paginatedArticles = await getLatestArticles(page, filter, selectedSources, selectedTags);

  return NextResponse.json(paginatedArticles);
}
