'use client';

// Removed unused imports - now handled by ArticleCard component
import { useState, useEffect, useRef, useCallback } from 'react';
import { useRouter } from "next/navigation";
import dynamic from 'next/dynamic';

import ArticleSkeleton from '../../components/ArticleSkeleton';
import ProgressBar from '@/components/ProgressBar';
import { Article } from '@/app/(frontend)/utils/article';
import { isMobileLayoutClient } from '@/utils/mobileLayout';

// Dynamic imports for better code splitting
const TagFilterBar = dynamic(() => import('@/components/TagFilterBar'), {
  loading: () => <div className="h-16 bg-gray-100 dark:bg-gray-800 animate-pulse rounded-lg mb-6" />
});

const ArticleCard = dynamic(() => import('@/components/ArticleCard'), {
  loading: () => <ArticleSkeleton />
});


export default function Home() {
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedSources, setSelectedSources] = useState<number[]>([]);
  const [showThumbnails, setShowThumbnails] = useState(true);
  const [articles, setArticles] = useState<Article[]>([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [isFilterLoading, setIsFilterLoading] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [isMobileLayout, setIsMobileLayout] = useState(false);
  const observerTarget = useRef<HTMLDivElement>(null);

    const router = useRouter();

  const fetchArticles = useCallback(async (pageNum: number) => {
    try {
      setLoading(true);
      // Show filter loading for page 1 (new filter applied)
      if (pageNum === 1) {
        setIsFilterLoading(true);
      }

      // Build query parameters
      const params = new URLSearchParams({
        page: pageNum.toString(),
        limit: '15',
        filter: 'Recent' // Always use Recent since we removed the filter buttons
      });

      // Add tags if any are selected
      if (selectedTags.length > 0) {
        params.set('tags', selectedTags.join(','));
      }

      // Add sources if any are selected
      if (selectedSources.length > 0) {
        params.append('sources', selectedSources.join(','));
      }

      const response = await fetch(`/api/articles?${params.toString()}`);
      const data = await response.json();

      // Check if the response has the expected structure
      if (data && data.articles && data.pagination) {
        setArticles(prev => pageNum === 1 ? data.articles : [...prev, ...data.articles]);
        setHasMore(data.pagination.hasMore);
      } else {
        console.error('Invalid response structure:', data);
        // Handle error case
        if (pageNum === 1) {
          setArticles([]);
        }
        setHasMore(false);
      }

    } catch (error) {
      console.error('Error fetching articles:', error);
    } finally {
      setLoading(false);
      setIsFilterLoading(false);
    }
  }, [selectedTags, selectedSources]);


  const handleClickAction = (article: Article) => {
    setIsNavigating(true);
    router.push('article/' + article.slug);
    // Reset navigation state after a short delay (route change should happen quickly)
    setTimeout(() => setIsNavigating(false), 1000);
  }

  useEffect(() => {
    setPage(1);
    fetchArticles(1);
  }, [selectedTags, selectedSources, fetchArticles]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && hasMore && !loading) {
          setPage(prev => prev + 1);
        }
      },
      { threshold: 1.0 }
    );

    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    return () => observer.disconnect();
  }, [hasMore, loading]);

  useEffect(() => {
    if (page > 1) {
      fetchArticles(page);
    }
  }, [page, fetchArticles]);

  // Check for mobile layout on mount
  useEffect(() => {
    setIsMobileLayout(isMobileLayoutClient());
  }, []);

  return (
    <>
      {/* Progress Bar - Hidden in mobile layout */}
      <ProgressBar isLoading={isFilterLoading || isNavigating} />

      {/* Source Filter Bar - Keep for all layouts */}
      <TagFilterBar
        selectedTags={selectedTags}
        setSelectedTags={setSelectedTags}
        selectedSources={selectedSources}
        setSelectedSources={setSelectedSources}
        onFilterChange={() => setIsFilterLoading(true)}
      />

      <div className={isMobileLayout ? "w-full p-4" : "max-w-[2000px] mx-auto p-6"}>
        {/* News Grid - Simplified for mobile layout */}
        <div className={isMobileLayout ?
          "grid grid-cols-1 gap-4" :
          "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6"
        }>
        {articles.map((article) => (
          <ArticleCard
            key={article.id}
            article={article}
            showThumbnails={showThumbnails}
            onClick={handleClickAction}
          />
        ))}

        {/* Loading Skeletons */}
        {loading && (
          <>
            <ArticleSkeleton />
            <ArticleSkeleton />
            <ArticleSkeleton />
            <ArticleSkeleton />
            <ArticleSkeleton />
          </>
        )}
      </div>

      {/* Intersection Observer Target */}
      <div ref={observerTarget} className="h-4 mt-8" />

      {/* End of Content Message */}
      {!hasMore && !loading && articles.length > 0 && (
        <p className="text-center text-gray-500 dark:text-gray-400 mt-8">
          No more articles to load
        </p>
      )}




    </div>
    </>
  );
}
