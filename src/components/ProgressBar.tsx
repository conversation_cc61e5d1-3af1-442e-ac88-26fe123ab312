"use client";

import { useEffect, useState } from 'react';

interface ProgressBarProps {
  isLoading: boolean;
  duration?: number; // Duration in milliseconds
}

export default function ProgressBar({ isLoading, duration = 800 }: ProgressBarProps) {
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    let timeout: NodeJS.Timeout;

    if (isLoading) {
      setIsVisible(true);
      setProgress(0);
      
      // Start with immediate progress to 30%
      setTimeout(() => setProgress(30), 50);
      
      // Gradually increase progress
      interval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) return prev; // Stop at 90% until loading completes
          return prev + Math.random() * 15;
        });
      }, duration / 10);
      
    } else if (isVisible) {
      // Complete the progress bar
      setProgress(100);
      
      // Hide after completion animation with a slight delay to show completion
      timeout = setTimeout(() => {
        setIsVisible(false);
        setProgress(0);
      }, 500);
    }

    return () => {
      if (interval) clearInterval(interval);
      if (timeout) clearTimeout(timeout);
    };
  }, [isLoading, duration, isVisible]);

  if (!isVisible) return null;

  return (
    <div className="fixed top-0 left-0 right-0 z-50 h-1 bg-gray-200/50 dark:bg-gray-800/50">
      <div
        className="h-full bg-gradient-to-r from-blue-500 via-blue-600 to-blue-500 transition-all duration-300 ease-out"
        style={{
          width: `${Math.min(progress, 100)}%`,
          boxShadow: progress > 0 ? '0 0 8px rgba(59, 130, 246, 0.6), 0 0 20px rgba(59, 130, 246, 0.3)' : 'none'
        }}
      />
    </div>
  );
}
