"use client";

import { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface SourceFilterBarProps {
  selectedTags: string[];
  setSelectedTags: (tags: string[]) => void;
  selectedSources: number[];
  setSelectedSources: (sources: number[]) => void;
  onFilterChange?: () => void; // Callback when filters change
}

interface Source {
  id: number;
  title: string;
  url: string;
  _count: {
    articles: number;
  };
}

export default function TagFilterBar({
  selectedTags,
  setSelectedTags,
  selectedSources,
  setSelectedSources,
  onFilterChange
}: SourceFilterBarProps) {
  const [availableSources, setAvailableSources] = useState<Source[]>([]);
  const [showAllSources, setShowAllSources] = useState(false);
  const [sourcesLoading, setSourcesLoading] = useState(true);

  // Number of sources to show initially
  const INITIAL_SOURCE_COUNT = 6;

  useEffect(() => {
    fetchSources();
  }, []);

  const fetchSources = async () => {
    try {
      const response = await fetch('/api/sources');
      const sources = await response.json();
      setAvailableSources(sources);
    } catch (error) {
      console.error('Error fetching sources:', error);
      setAvailableSources([]);
    } finally {
      setSourcesLoading(false);
    }
  };

  const handleSourceToggle = (sourceId: number) => {
    setSelectedSources(
      selectedSources.includes(sourceId)
        ? selectedSources.filter(id => id !== sourceId)
        : [...selectedSources, sourceId]
    );
    // Trigger filter change callback
    if (onFilterChange) {
      onFilterChange();
    }
  };

  const visibleSources = showAllSources ? availableSources : availableSources.slice(0, INITIAL_SOURCE_COUNT);
  const hasMoreSources = availableSources.length > INITIAL_SOURCE_COUNT;

  return (
    <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 px-6 py-4">
      <div className="max-w-[2000px] mx-auto">
        {/* Source Filter Buttons */}
        <div className="mb-2">
          <div className="flex gap-2 overflow-x-auto scrollbar-hide pb-2">
            {sourcesLoading ? (
              // Loading skeleton
              Array.from({ length: 6 }).map((_, i) => (
                <div
                  key={i}
                  className="h-8 w-24 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse flex-shrink-0"
                />
              ))
            ) : (
              <>
                {visibleSources.map((source) => (
                  <button
                    key={source.id}
                    onClick={() => handleSourceToggle(source.id)}
                    className={`flex-shrink-0 px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 whitespace-nowrap ${
                      selectedSources.includes(source.id)
                        ? 'bg-blue-500 text-white shadow-md'
                        : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
                    }`}
                  >
                    {source.title}
                  </button>
                ))}

                {/* Show All/Less Button */}
                {hasMoreSources && (
                  <button
                    onClick={() => setShowAllSources(!showAllSources)}
                    className="flex-shrink-0 flex items-center gap-1 px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                  >
                    {showAllSources ? (
                      <>
                        Less <ChevronUp className="w-4 h-4" />
                      </>
                    ) : (
                      <>
                        All <ChevronDown className="w-4 h-4" />
                      </>
                    )}
                  </button>
                )}
              </>
            )}
          </div>
        </div>



        {/* Selected Filters Summary */}
        {(selectedTags.length > 0 || selectedSources.length > 0) && (
          <div className="mt-3 flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <span>Filtering by:</span>
            <div className="flex gap-1 flex-wrap">
              {selectedSources.length > 0 && (
                <div className="flex gap-1">
                  <span className="text-blue-600 dark:text-blue-400 font-medium">Sources:</span>
                  {selectedSources.map((sourceId, index) => {
                    const source = availableSources.find(s => s.id === sourceId);
                    return (
                      <span key={sourceId}>
                        {source?.title}
                        {index < selectedSources.length - 1 && ', '}
                      </span>
                    );
                  })}
                  {selectedTags.length > 0 && <span className="mx-1">•</span>}
                </div>
              )}
              {selectedTags.length > 0 && (
                <div className="flex gap-1">
                  <span className="text-green-600 dark:text-green-400 font-medium">Tags:</span>
                  {selectedTags.map((tag, index) => (
                    <span key={tag}>
                      {tag}
                      {index < selectedTags.length - 1 && ', '}
                    </span>
                  ))}
                </div>
              )}
            </div>
            <button
              onClick={() => {
                setSelectedTags([]);
                setSelectedSources([]);
                // Trigger filter change callback
                if (onFilterChange) {
                  onFilterChange();
                }
              }}
              className="text-blue-600 dark:text-blue-400 hover:underline ml-2"
            >
              Clear all
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
