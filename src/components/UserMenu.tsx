'use client';

import {
  SignIn<PERSON>utton,
  SignU<PERSON><PERSON>utton,
  SignedIn,
  SignedOut,
  UserButton,
} from '@clerk/nextjs';

export default function UserMenu() {
  return (
    <div className="flex items-center space-x-4">
      <SignedOut>
        <div className="flex items-center space-x-2">
          <SignInButton>
            <button className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              Sign in
            </button>
          </SignInButton>
          <SignUpButton>
            <button className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              Sign up
            </button>
          </SignUpButton>
        </div>
      </SignedOut>
      <SignedIn>
        <UserButton
          appearance={{
            elements: {
              avatarBox: "w-8 h-8",
            },
          }}
        />
      </SignedIn>
    </div>
  );
}
