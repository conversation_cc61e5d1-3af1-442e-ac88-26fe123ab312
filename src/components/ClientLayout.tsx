"use client";

import { ReactNode, useEffect, useState } from 'react';
import Link from 'next/link';
import ThemeToggle from './ThemeToggle';
import UserMenu from './UserMenu';
import Footer from './Footer';
import { isMobileLayoutClient } from '@/utils/mobileLayout';

export default function ClientLayout({ children }: { children: ReactNode }) {
    const [isMobileLayout, setIsMobileLayout] = useState(false);

    useEffect(() => {
        setIsMobileLayout(isMobileLayoutClient());
    }, []);

    // Mobile layout - clean version without header but with footer
    if (isMobileLayout) {
        return (
            <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
                {/* Clean main content with footer */}
                <main className="w-full">
                    {children}
                </main>
                <Footer />
            </div>
        );
    }

    // Standard desktop layout
    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
            {/* Header */}
            <header className="topmenubar h-16 fixed top-0 left-0 right-0 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 z-10">
                <div className="flex items-center justify-between h-full px-6">
                    <Link href="/" className="text-2xl font-bold text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                        Tech News
                    </Link>
                    <div className="flex items-center space-x-4">
                        <ThemeToggle/>
                        <UserMenu/>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className="pt-16 flex-1">
                {children}
            </main>

            {/* Footer */}
            <Footer />
        </div>
    );
}