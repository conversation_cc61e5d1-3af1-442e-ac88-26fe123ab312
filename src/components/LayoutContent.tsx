import { ReactNode, useState, useEffect } from 'react';
import ThemeToggle from './ThemeToggle';
import UserMenu from './UserMenu';

export default function LayoutContent({ children }: { children: ReactNode }) {
    // Start with mobile-first approach to prevent layout shifts
    const [isMobile, setIsMobile] = useState(true);
    const [sidebarOpen, setSidebarOpen] = useState(false);

    useEffect(() => {

        const checkScreenSize = () => {
            const mobile = window.innerWidth < 768;
            setIsMobile(mobile);
        };

        const handleSidebarToggle = (e: CustomEvent) => {
            setSidebarOpen(e.detail.isOpen);
        };

        // Initialize
        checkScreenSize();

        // Set up event listeners
        window.addEventListener('resize', checkScreenSize);
        window.addEventListener('sidebarToggle', handleSidebarToggle as EventListener);

        return () => {
            window.removeEventListener('resize', checkScreenSize);
            window.removeEventListener('sidebarToggle', handleSidebarToggle as EventListener);
        };
    }, []);

    return (
        <div className={`flex-1 ml-0 md:ml-64 transition-all duration-300 ${
            !isMobile && !sidebarOpen ? 'md:ml-0' : ''
        }`}>
            <header
                className={`h-16 fixed top-0 left-0 md:left-64 right-0 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 z-10 transition-all duration-300 ${
                    !isMobile && !sidebarOpen ? 'md:left-0' : ''
                }`}>
                <div className="flex items-center justify-between h-full px-6">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Tech News</h1>
                    <div className="flex items-center space-x-4">
                        <ThemeToggle/>
                        <UserMenu/>
                    </div>
                </div>
            </header>
            <main className="pt-16">
                {children}
            </main>
        </div>
    );
}