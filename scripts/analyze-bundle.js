#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Function to analyze JavaScript files for legacy patterns
function analyzeLegacyJS(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    const legacyPatterns = [
      { pattern: /function\s*\(/g, name: 'function declarations' },
      { pattern: /var\s+/g, name: 'var declarations' },
      { pattern: /\.prototype\./g, name: 'prototype usage' },
      { pattern: /Object\.defineProperty/g, name: 'Object.defineProperty' },
      { pattern: /_createClass/g, name: 'Babel class transforms' },
      { pattern: /_inherits/g, name: 'Babel inheritance transforms' },
      { pattern: /regeneratorRuntime/g, name: 'regenerator runtime' },
    ];

    const results = {};
    let totalMatches = 0;

    legacyPatterns.forEach(({ pattern, name }) => {
      const matches = content.match(pattern);
      if (matches) {
        results[name] = matches.length;
        totalMatches += matches.length;
      }
    });

    return { results, totalMatches, size: content.length };
  } catch (error) {
    return { error: error.message };
  }
}

// Analyze .next/static/chunks directory
const chunksDir = path.join(path.dirname(__dirname), '.next', 'static', 'chunks');

if (!fs.existsSync(chunksDir)) {
  console.log('❌ Build directory not found. Run "npm run build" first.');
  process.exit(1);
}

console.log('🔍 Analyzing JavaScript bundles for legacy code...\n');

const files = fs.readdirSync(chunksDir)
  .filter(file => file.endsWith('.js'))
  .slice(0, 5); // Analyze first 5 files

let totalLegacyMatches = 0;
let totalSize = 0;

files.forEach(file => {
  const filePath = path.join(chunksDir, file);
  const analysis = analyzeLegacyJS(filePath);
  
  if (analysis.error) {
    console.log(`❌ Error analyzing ${file}: ${analysis.error}`);
    return;
  }

  totalSize += analysis.size;
  totalLegacyMatches += analysis.totalMatches;

  console.log(`📄 ${file} (${(analysis.size / 1024).toFixed(1)}KB)`);
  
  if (analysis.totalMatches === 0) {
    console.log('   ✅ No legacy JavaScript patterns found');
  } else {
    Object.entries(analysis.results).forEach(([pattern, count]) => {
      console.log(`   ⚠️  ${count} ${pattern}`);
    });
  }
  console.log('');
});

console.log(`📊 Summary:`);
console.log(`   Total files analyzed: ${files.length}`);
console.log(`   Total size: ${(totalSize / 1024).toFixed(1)}KB`);
console.log(`   Legacy patterns found: ${totalLegacyMatches}`);

if (totalLegacyMatches === 0) {
  console.log('   🎉 Great! Your bundles are using modern JavaScript!');
} else {
  console.log(`   💡 Consider updating TypeScript target or dependencies to reduce legacy code.`);
}
